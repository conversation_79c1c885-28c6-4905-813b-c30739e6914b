/**
 * Read Manga Page CSS with prefixed class names to avoid conflicts
 * Use GM_addStyle for CSS injection in userscripts
 */

function addReadMangaPageCSS() {
    const css = `
        /* Read Manga Page Container */
        .g-read-manga-page {
            min-height: 100vh;
            background: #1a1a1a;
            color: #fff;
        }

        /* Gallery Grid Layout with g- prefix */
        .g-gallery-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 25px;
            padding: 20px 10px;
            margin: 0 auto;
            max-width: 1200px;
        }

        /* Gallery Container for offline favorites compatibility */
        .g-gallery-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 25px;
            padding: 0 10px;
        }

        /* Individual Gallery Item */
        .g-gallery-item {
            position: relative;
            border-radius: 3px;
            overflow: hidden;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.4);
            transition: all 0.3s ease;
            background: #252525;
        }

        .g-gallery-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        }

        /* Gallery Cover Image */
        .g-gallery-cover {
            position: relative;
            width: 100%;
            height: 300px;
            overflow: hidden;
        }

        .g-gallery-cover img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .g-gallery-item:hover .g-gallery-cover img {
            transform: scale(1.05);
        }

        /* Gallery Caption/Title */
        .g-gallery-caption {
            padding: 10px;
            background: rgba(0, 0, 0, 0.8);
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            font-size: 12px;
            line-height: 1.3;
            max-height: 60px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
        }

        /* Remove from Read List Button */
        .g-remove-read-btn {
            position: absolute;
            top: 5px;
            right: 5px;
            width: 28px;
            height: 28px;
            background: rgba(244, 67, 54, 0.9);
            border: none;
            border-radius: 50%;
            color: white;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            z-index: 10;
        }

        .g-remove-read-btn:hover {
            background: rgba(244, 67, 54, 1);
            transform: scale(1.1);
        }

        /* Read Manga Controls */
        .g-read-controls {
            margin: 20px 0;
            padding: 15px;
            background: rgba(0,0,0,0.1);
            border-radius: 5px;
        }

        .g-controls-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 10px;
        }

        /* Action Buttons */
        .g-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            transition: all 0.3s ease;
        }

        .g-btn-danger {
            background: #f44336;
            color: white;
        }

        .g-btn-danger:hover {
            background: #d32f2f;
            transform: translateY(-1px);
        }

        .g-btn-secondary {
            background: #666;
            color: white;
        }

        .g-btn-secondary:hover {
            background: #555;
            transform: translateY(-1px);
        }

        .g-btn-primary {
            background: #ed2553;
            color: white;
        }

        .g-btn-primary:hover {
            background: #c91e47;
            transform: translateY(-1px);
        }

        /* Sort Controls */
        .g-sort-controls select {
            background: #333;
            color: #fff;
            border: 1px solid #555;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
        }

        .g-sort-controls select:hover {
            background-color: #3d3d3d;
        }

        /* Empty State */
        .g-empty-state {
            text-align: center;
            padding: 60px 20px;
        }

        .g-empty-state i {
            font-size: 64px;
            color: #666;
            margin-bottom: 20px;
        }

        .g-empty-state h2 {
            color: #666;
            margin-bottom: 10px;
        }

        .g-empty-state p {
            color: #999;
            margin-bottom: 30px;
        }

        /* No Image Placeholder */
        .g-no-image-placeholder {
            width: 100%;
            height: 300px;
            background: linear-gradient(135deg, #333 0%, #555 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 48px;
            transition: all 0.3s ease;
        }

        .g-gallery-item:hover .g-no-image-placeholder {
            background: linear-gradient(135deg, #444 0%, #666 100%);
        }

        .g-no-image-placeholder i {
            transition: all 0.3s ease;
        }

        .g-gallery-item:hover .g-no-image-placeholder i {
            transform: scale(1.1);
            opacity: 0.7;
        }

        /* Responsive Design - Mobile Breakpoint at 768px */
        @media (max-width: 768px) {
            .g-gallery-grid,
            .g-gallery-container {
                grid-template-columns: repeat(auto-fill, minmax(115px, 1fr));
                gap: 15px;
                padding: 15px 5px;
            }

            .g-gallery-cover {
                height: 180px;
            }

            .g-gallery-caption {
                font-size: 10px;
                padding: 8px;
                max-height: 50px;
                -webkit-line-clamp: 2;
            }

            .g-remove-read-btn {
                width: 24px;
                height: 24px;
                font-size: 12px;
            }

            .g-controls-row {
                flex-direction: column;
                align-items: stretch;
            }

            .g-btn {
                justify-content: center;
                margin-bottom: 10px;
            }

            .g-empty-state {
                padding: 40px 15px;
            }

            .g-empty-state i {
                font-size: 48px;
            }

            .g-empty-state h2 {
                font-size: 1.5em;
            }
        }

        /* Large Screen Enhancements */
        @media (min-width: 1400px) {
            .g-gallery-grid,
            .g-gallery-container {
                grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
                gap: 30px;
            }

            .g-gallery-cover {
                height: 350px;
            }
        }

        /* Hover Effects and Animations */
        .g-gallery-item {
            transition: all 0.3s ease;
        }

        .g-gallery-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.4);
        }

        /* Smooth transitions for all interactive elements */
        .g-gallery-item * {
            transition: opacity 0.3s ease, transform 0.3s ease, filter 0.3s ease;
        }

        /* Backdrop filter for better readability */
        .g-remove-read-btn,
        .g-gallery-caption {
            backdrop-filter: blur(4px);
            -webkit-backdrop-filter: blur(4px);
        }

        /* Dark Mode Compatibility */
        @media (prefers-color-scheme: dark) {
            .g-btn {
                box-shadow: 0 2px 8px rgba(0,0,0,0.4);
            }

            .g-gallery-item {
                background: #1e1e1e;
                border: 1px solid #333;
            }
        }

        /* Force layout for better gallery density when needed */
        @media screen and (min-width: 980px) {
            .g-gallery-grid .gallery,
            .g-gallery-grid .gallery-favorite,
            .g-gallery-grid .thumb-container {
                width: 19% !important;
                margin: 3px !important;
            }
        }
    `;

    // Use GM_addStyle for userscript CSS injection
    if (typeof GM !== 'undefined' && GM.addStyle) {
        GM.addStyle(css);
    } else if (typeof GM_addStyle !== 'undefined') {
        GM_addStyle(css);
    } else {
        // Fallback for environments without GM functions
        const style = document.createElement('style');
        style.type = 'text/css';
        style.appendChild(document.createTextNode(css));
        document.head.appendChild(style);
    }
}

// Export for use in userscripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { addReadMangaPageCSS };
}
